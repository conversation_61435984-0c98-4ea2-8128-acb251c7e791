{"name": "docmobile_portal_frontend_react", "version": "0.1.0", "private": true, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "^2.1.5", "@radix-ui/react-separator": "^1.1.3", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.4", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-tooltip": "^1.2.4", "@reduxjs/toolkit": "^2.5.1", "@svgr/webpack": "^8.1.0", "@tailwindcss/typography": "^0.5.16", "@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "add": "^2.0.6", "apexcharts": "^4.4.0", "axios": "^1.7.9", "breadcrumb": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.5.2", "embla-carousel-react": "^8.5.2", "framer-motion": "^12.9.2", "i18next": "^25.0.2", "i18next-browser-languagedetector": "^8.0.5", "i18next-http-backend": "^3.0.2", "input-otp": "^1.4.2", "libphonenumber-js": "^1.12.8", "lucide-react": "^0.474.0", "prop-types": "^15.8.1", "react": "^18.0.0", "react-apexcharts": "^1.7.0", "react-big-calendar": "^1.18.0", "react-day-picker": "8.10.1", "react-dom": "^18.0.0", "react-helmet": "^6.1.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.5.1", "react-icons": "^5.4.0", "react-intersection-observer": "^9.16.0", "react-phone-input-2": "^2.15.1", "react-redux": "^9.2.0", "react-router-dom": "^7.1.3", "react-scripts": "5.0.1", "shadcn": "^2.4.0", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-textshadow": "^2.1.3", "web-vitals": "^2.1.0", "zod": "^3.24.1"}, "scripts": {"start": "set REACT_APP_ENV=stag&& react-scripts start", "start:dev": "set REACT_APP_ENV=dev&& react-scripts start", "start:prod": "set REACT_APP_ENV=prod&& react-scripts start", "build": "set REACT_APP_ENV=stag&& react-scripts build", "build:dev": "set REACT_APP_ENV=dev&& react-scripts build", "build:prod": "set REACT_APP_ENV=prod&& react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/js": "^9.27.0", "@types/react": "^19.0.8", "@types/react-dom": "^19.0.3", "@typescript-eslint/eslint-plugin": "^8.32.1", "@typescript-eslint/parser": "^8.32.1", "autoprefixer": "^10.4.20", "eslint-config-react-app": "^7.0.1", "eslint-plugin-react": "^7.37.5", "eslint-plugin-unused-imports": "^4.1.4", "globals": "^16.2.0", "postcss": "^8.5.1", "prettier": "^3.4.2", "tailwindcss": "3.4.17", "typescript": "^5.7.3"}}