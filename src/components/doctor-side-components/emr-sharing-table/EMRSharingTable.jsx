import React from "react";
import { useState, useMemo } from "react";
import { Input } from "../../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../ui/table";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationLink,
} from "../../ui/pagination";
import { Button } from "../../ui/button";
import { ReactComponent as Eye } from "../../../assets/svgs/eye.svg";
import { useNavigate } from "react-router-dom";
import SharedPatientEMRData from "../../data/SharedPatientEMRData";

const EMRSharingTable = () => {
  const [search, setSearch] = useState("");
  const [dateFilter, setDateFilter] = useState("All Time");
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;
  const navigate = useNavigate();

  // Set the current date to 21/5/2025 to match our data
  const today = new Date(2025, 4, 21); // Month is 0-indexed, so 4 = May

  // Helper function to get the start and end of a week
  const getWeekRange = (date) => {
    const day = date.getDay();
    const diff = date.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    const monday = new Date(date);
    monday.setDate(diff);
    monday.setHours(0, 0, 0, 0);

    const sunday = new Date(monday);
    sunday.setDate(monday.getDate() + 6);
    sunday.setHours(23, 59, 59, 999);

    return { start: monday, end: sunday };
  };

  // Get current date and week ranges
  const thisWeekRange = getWeekRange(today);

  // Get last week's range
  const lastWeekStart = new Date(thisWeekRange.start);
  lastWeekStart.setDate(lastWeekStart.getDate() - 7);
  const lastWeekEnd = new Date(thisWeekRange.start);
  lastWeekEnd.setDate(lastWeekEnd.getDate() - 1);
  lastWeekEnd.setHours(23, 59, 59, 999);

  // Get next week's range
  const nextWeekStart = new Date(thisWeekRange.end);
  nextWeekStart.setDate(nextWeekStart.getDate() + 1);
  const nextWeekEnd = new Date(nextWeekStart);
  nextWeekEnd.setDate(nextWeekStart.getDate() + 6);
  nextWeekEnd.setHours(23, 59, 59, 999);

  // Calculate date filter counts
  const dateFilterCounts = useMemo(() => {
    return {
      "This Week": SharedPatientEMRData.filter(
        (p) => p.date >= thisWeekRange.start && p.date <= thisWeekRange.end,
      ).length,
      "Last Week": SharedPatientEMRData.filter(
        (p) => p.date >= lastWeekStart && p.date <= lastWeekEnd,
      ).length,
      "Next Week": SharedPatientEMRData.filter(
        (p) => p.date >= nextWeekStart && p.date <= nextWeekEnd,
      ).length,
    };
  }, []);

  // Filter data based on search and date range
  const filteredData = useMemo(() => {
    return SharedPatientEMRData.filter((record) => {
      // Search filter (check patient name, ID, medical concern, shared by, and shared to)
      const searchTerms = search.toLowerCase();
      const matchesSearch =
        search === "" ||
        record.patient.toLowerCase().includes(searchTerms) ||
        record.id.toLowerCase().includes(searchTerms) ||
        record.medicalConcern.toLowerCase().includes(searchTerms) ||
        record.sharedBy.toLowerCase().includes(searchTerms) ||
        record.sharedTo.toLowerCase().includes(searchTerms);

      // Date filter
      let matchesDateRange = true;
      if (dateFilter === "This Week") {
        matchesDateRange =
          record.date >= thisWeekRange.start &&
          record.date <= thisWeekRange.end;
      } else if (dateFilter === "Last Week") {
        matchesDateRange =
          record.date >= lastWeekStart && record.date <= lastWeekEnd;
      } else if (dateFilter === "Next Week") {
        matchesDateRange =
          record.date >= nextWeekStart && record.date <= nextWeekEnd;
      }

      return matchesSearch && matchesDateRange;
    });
  }, [search, dateFilter]);

  const clearFilters = () => {
    setSearch("");
    setDateFilter("All Time");
    setCurrentPage(1);
  };

  const totalPages = Math.ceil(filteredData.length / itemsPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage,
  );

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-bold">List of shared patient EMR</h2>
        <div className="flex gap-4 items-center">
          <Input
            placeholder="Search patient, doctor, or medical concern"
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-64"
          />
          <Select
            value={dateFilter}
            onValueChange={(value) => {
              setDateFilter(value);
              setCurrentPage(1); // Reset to first page when filter changes
            }}
          >
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All Time">All Time</SelectItem>
              <SelectItem value="This Week">
                This Week ({dateFilterCounts["This Week"]})
              </SelectItem>
              <SelectItem value="Last Week">
                Last Week ({dateFilterCounts["Last Week"]})
              </SelectItem>
              <SelectItem value="Next Week">
                Next Week ({dateFilterCounts["Next Week"]})
              </SelectItem>
            </SelectContent>
          </Select>
          {(search || dateFilter !== "All Time") && (
            <Button variant="outline" onClick={clearFilters} size="sm">
              Clear Filters
            </Button>
          )}
        </div>
      </div>

      <Table>
        <TableHeader className="bg-[#DFE0E2]">
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Patient Name</TableHead>
            {/* <TableHead>Location</TableHead> */}
            <TableHead>Medical Concern</TableHead>
            <TableHead>Date</TableHead>
            <TableHead>Time</TableHead>
            <TableHead>Shared By</TableHead>
            <TableHead>Shared To</TableHead>
            <TableHead>Shared Date</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {paginatedData.map((record) => (
            <TableRow key={record.id}>
              <TableCell>{record.id}</TableCell>
              <TableCell>{record.patient}</TableCell>
              {/* <TableCell>{record.location}</TableCell> */}
              <TableCell>{record.medicalConcern}</TableCell>
              <TableCell>{record.formattedDate}</TableCell>
              <TableCell>{record.time}</TableCell>
              <TableCell>{record.sharedBy}</TableCell>
              <TableCell>{record.sharedTo}</TableCell>
              <TableCell>{record.formattedSharedDate}</TableCell>
              <TableCell>
                <Button
                  variant="ghost"
                  size="default"
                  onClick={() => navigate("/doctor/patient-profile")}
                >
                  <Eye />
                </Button>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      {filteredData.length === 0 && (
        <div className="py-8 text-center text-gray-500">
          No EMR records found matching your filters.
        </div>
      )}

      {filteredData.length > 0 && (
        <div className="mt-4 w-full">
          <div className="text-sm text-gray-500 mb-4">
            Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
            {Math.min(currentPage * itemsPerPage, filteredData.length)} of{" "}
            {filteredData.length} EMR records
          </div>
          <div className="flex justify-end">
            <Pagination>
              <PaginationContent>
                <PaginationItem>
                  <PaginationPrevious
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                    className={
                      currentPage === 1
                        ? "opacity-50 cursor-not-allowed"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>

                {/* Show up to 5 page numbers */}
                {Array.from({ length: Math.min(totalPages, 5) }, (_, i) => {
                  let pageNumber;
                  if (totalPages <= 5) {
                    pageNumber = i + 1;
                  } else {
                    // Calculate which pages to show based on current page
                    if (currentPage <= 3) {
                      pageNumber = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNumber = totalPages - 4 + i;
                    } else {
                      pageNumber = currentPage - 2 + i;
                    }
                  }

                  return (
                    <PaginationItem key={pageNumber}>
                      <PaginationLink
                        isActive={currentPage === pageNumber}
                        onClick={() => handlePageChange(pageNumber)}
                        className="cursor-pointer"
                      >
                        {pageNumber}
                      </PaginationLink>
                    </PaginationItem>
                  );
                })}

                <PaginationItem>
                  <PaginationNext
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                    className={
                      currentPage === totalPages
                        ? "opacity-50 cursor-not-allowed"
                        : "cursor-pointer"
                    }
                  />
                </PaginationItem>
              </PaginationContent>
            </Pagination>
          </div>
        </div>
      )}
    </div>
  );
};

export default EMRSharingTable;
