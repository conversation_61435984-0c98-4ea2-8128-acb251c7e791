import React from "react";
import { useState, useEffect } from "react";
import { Eye, Download, Search } from "lucide-react";
import { useNavigate } from "react-router-dom";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../ui/table";
import { Input } from "../../ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../ui/select";
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationPrevious,
  PaginationNext,
  PaginationLink,
} from "../../ui/pagination";
import { Avatar, AvatarFallback, AvatarImage } from "../../ui/avatar";
import PrescriptionData from "./PrescriptionData";
import FadeInSection from "../../animations/FadeInSection";
import DefaultAvatar from "../../../assets/images/DefaultAvatar.png";

const PatientPrescriptionTable = () => {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedTimeframe, setSelectedTimeframe] = useState("All");
  const [currentPage, setCurrentPage] = useState(1);
  const navigate = useNavigate();

  const ITEMS_PER_PAGE = 20;

  // Helper function to check if a date is within the selected timeframe range
  const isDateInRange = (dateStr, range) => {
    if (range === "All") return true;

    const today = new Date(2025, 4, 21); // Fixed date: May 21, 2025
    const dateParts = dateStr.split("/");
    const date = new Date(
      Number.parseInt(dateParts[2]), // Year
      Number.parseInt(dateParts[1]) - 1, // Month (0-indexed)
      Number.parseInt(dateParts[0]), // Day
    );

    // Set to beginning of the day for accurate comparison
    today.setHours(0, 0, 0, 0);

    // Calculate the start of the current week (Monday)
    const startOfThisWeek = new Date(today);
    const day = today.getDay();
    const diff = today.getDate() - day + (day === 0 ? -6 : 1); // Adjust when day is Sunday
    startOfThisWeek.setDate(diff);
    startOfThisWeek.setHours(0, 0, 0, 0);

    // Calculate the end of the current week (Sunday)
    const endOfThisWeek = new Date(startOfThisWeek);
    endOfThisWeek.setDate(startOfThisWeek.getDate() + 6);
    endOfThisWeek.setHours(23, 59, 59, 999);

    // Calculate the start of last week
    const startOfLastWeek = new Date(startOfThisWeek);
    startOfLastWeek.setDate(startOfThisWeek.getDate() - 7);

    // Calculate the end of last week
    const endOfLastWeek = new Date(startOfLastWeek);
    endOfLastWeek.setDate(startOfLastWeek.getDate() + 6);
    endOfLastWeek.setHours(23, 59, 59, 999);

    // Calculate the start of this month
    const startOfThisMonth = new Date(today.getFullYear(), today.getMonth(), 1);

    // Calculate the end of this month
    const endOfThisMonth = new Date(
      today.getFullYear(),
      today.getMonth() + 1,
      0,
    );
    endOfThisMonth.setHours(23, 59, 59, 999);

    // Calculate the start of last month
    const startOfLastMonth = new Date(
      today.getFullYear(),
      today.getMonth() - 1,
      1,
    );

    // Calculate the end of last month
    const endOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0);
    endOfLastMonth.setHours(23, 59, 59, 999);

    switch (range) {
      case "This Week":
        return date >= startOfThisWeek && date <= endOfThisWeek;
      case "Last Week":
        return date >= startOfLastWeek && date <= endOfLastWeek;
      case "This Month":
        return date >= startOfThisMonth && date <= endOfThisMonth;
      case "Last Month":
        return date >= startOfLastMonth && date <= endOfLastMonth;
      default:
        return true;
    }
  };

  // Filter prescriptions based on search query and selected timeframe
  const filteredPrescriptions = PrescriptionData.filter((prescription) => {
    // Search filter (Prescription ID, Doctor Name, Email, Contact, Service, Location)
    const matchesSearch =
      prescription.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      prescription.doctorName
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      prescription.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
      prescription.contactNumber
        .toLowerCase()
        .includes(searchQuery.toLowerCase()) ||
      prescription.service.toLowerCase().includes(searchQuery.toLowerCase()) ||
      prescription.location.toLowerCase().includes(searchQuery.toLowerCase());

    // Date filter
    const matchesDateFilter = isDateInRange(
      prescription.date,
      selectedTimeframe,
    );

    return matchesSearch && matchesDateFilter;
  });

  const totalPages = Math.ceil(filteredPrescriptions.length / ITEMS_PER_PAGE);
  const currentData = filteredPrescriptions.slice(
    (currentPage - 1) * ITEMS_PER_PAGE,
    currentPage * ITEMS_PER_PAGE,
  );

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery, selectedTimeframe]);

  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  const handleViewPrescription = () => {
    navigate(`/patient/prescription-details`);
  };

  return (
    <div className="w-full flex p-4 md:p-8 lg:p-8 flex-col gap-4">
      <FadeInSection>
        <h1 className="text-2xl font-semibold mb-6">Prescriptions</h1>

        <div className="flex justify-between items-center mb-6">
          <div className="relative w-full max-w-md">
            <div className="absolute inset-y-0 left-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <Input
              type="text"
              placeholder="Search by ID, Doctor, Email, Contact, Service, or Location"
              className="pl-10"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Select
            value={selectedTimeframe}
            onValueChange={setSelectedTimeframe}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="All" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="This Week">This Week</SelectItem>
              <SelectItem value="Last Week">Last Week</SelectItem>
              <SelectItem value="This Month">This Month</SelectItem>
              <SelectItem value="Last Month">Last Month</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="">
          <Table>
            <TableHeader className="bg-[#DFE0E2]">
              <TableRow>
                <TableHead>Prescription ID</TableHead>
                <TableHead>Doctor Name</TableHead>
                <TableHead>Email Address</TableHead>
                <TableHead>Contact Number</TableHead>
                <TableHead>Service</TableHead>
                {/* <TableHead>Location</TableHead> */}
                <TableHead>Date</TableHead>
                <TableHead>Time</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.map((prescription, index) => (
                <TableRow key={index}>
                  <TableCell>{prescription.id}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2 flex-row">
                      <div className="h-9	w-9">
                        <Avatar className="w-full h-full object-cover ">
                          <AvatarImage
                            src={prescription?.profileImage || DefaultAvatar}
                          />
                          <AvatarFallback>{`${prescription.firstName} ${prescription.lastName}`}</AvatarFallback>
                        </Avatar>
                      </div>
                      {prescription.doctorName}
                    </div>
                  </TableCell>
                  <TableCell>{prescription.email}</TableCell>
                  <TableCell>{prescription.contactNumber}</TableCell>
                  <TableCell>{prescription.service}</TableCell>
                  {/* <TableCell>{prescription.location}</TableCell> */}
                  <TableCell>{prescription.date}</TableCell>
                  <TableCell>{prescription.time}</TableCell>
                  <TableCell>
                    <div className="flex space-x-3">
                      <button className="text-gray-500 hover:text-blue-600">
                        <Download size={20} />
                      </button>
                      <button
                        className="text-gray-500 hover:text-blue-600"
                        onClick={() => handleViewPrescription(prescription.id)}
                      >
                        <Eye size={20} />
                      </button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>

          {filteredPrescriptions.length === 0 && (
            <div className="py-8 text-center text-gray-500">
              No prescriptions found matching your filters.
            </div>
          )}

          {filteredPrescriptions.length > 0 && (
            <div className="flex justify-between items-center mt-4 w-full">
              <Pagination className="mt-4">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                    />
                  </PaginationItem>

                  {totalPages <= 5 ? (
                    // If 5 or fewer pages, show all page numbers
                    Array.from({ length: totalPages }, (_, i) => (
                      <PaginationItem key={i}>
                        <PaginationLink
                          isActive={currentPage === i + 1}
                          onClick={() => handlePageChange(i + 1)}
                        >
                          {i + 1}
                        </PaginationLink>
                      </PaginationItem>
                    ))
                  ) : (
                    // If more than 5 pages, show limited page numbers with ellipsis
                    <>
                      {/* First page always shown */}
                      <PaginationItem>
                        <PaginationLink
                          isActive={currentPage === 1}
                          onClick={() => handlePageChange(1)}
                        >
                          1
                        </PaginationLink>
                      </PaginationItem>

                      {/* Show ellipsis if current page is > 3 */}
                      {currentPage > 3 && (
                        <PaginationItem>
                          <span className="flex h-9 w-9 items-center justify-center text-sm">
                            ...
                          </span>
                        </PaginationItem>
                      )}

                      {/* Pages around current page */}
                      {Array.from({ length: totalPages }, (_, i) => i + 1)
                        .filter((page) => {
                          if (page === 1 || page === totalPages) return false; // Skip first and last pages (handled separately)
                          return Math.abs(page - currentPage) < 2; // Show pages within 1 of current page
                        })
                        .map((page) => (
                          <PaginationItem key={page}>
                            <PaginationLink
                              isActive={currentPage === page}
                              onClick={() => handlePageChange(page)}
                            >
                              {page}
                            </PaginationLink>
                          </PaginationItem>
                        ))}

                      {/* Show ellipsis if current page is < totalPages - 2 */}
                      {currentPage < totalPages - 2 && (
                        <PaginationItem>
                          <span className="flex h-9 w-9 items-center justify-center text-sm">
                            ...
                          </span>
                        </PaginationItem>
                      )}

                      {/* Last page always shown */}
                      <PaginationItem>
                        <PaginationLink
                          isActive={currentPage === totalPages}
                          onClick={() => handlePageChange(totalPages)}
                        >
                          {totalPages}
                        </PaginationLink>
                      </PaginationItem>
                    </>
                  )}

                  <PaginationItem>
                    <PaginationNext
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === totalPages}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            </div>
          )}
        </div>
      </FadeInSection>
    </div>
  );
};

export default PatientPrescriptionTable;
