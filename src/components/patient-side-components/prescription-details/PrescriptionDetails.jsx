import React from "react";
import { Download } from "lucide-react";
import FadeInSection from "../../animations/FadeInSection";
import { Button } from "../../ui/button";
import { ReactComponent as SignaturePlaceholder } from "../../../assets/svgs/SignPlaceholder.svg";

const PrescriptionDetails = () => {
  const mockData = {
    id: 1,
    doctorName: "William Panday",
    medications: [
      {
        drugName: "Panadol Tab",
        dosage: "1 Tablet",
        instruction: "Don't take if you're suffering High BP",
        duration: "7 Days",
        frequency: "10 mg",
      },
      {
        drugName: "Panadol Tab",
        dosage: "1 Tablet",
        instruction: "Don't take if you're suffering High BP",
        duration: "7 Days",
        frequency: "10 mg",
      },
      {
        drugName: "Panadol Tab",
        dosage: "1 Tablet",
        instruction: "Don't take if you're suffering High BP",
        duration: "7 Days",
        frequency: "10 mg",
      },
      {
        drugName: "Panadol Tab",
        dosage: "1 Tablet",
        instruction: "Don't take if you're suffering High BP",
        duration: "7 Days",
        frequency: "10 mg",
      },
      {
        drugName: "Panadol Tab",
        dosage: "1 Tablet",
        instruction: "Don't take if you're suffering High BP",
        duration: "7 Days",
        frequency: "10 mg",
      },
    ],
    signature: "William Panday",
    date: "01/08/2024",
  };

  console.log("prescriptionDetails", mockData);

  return (
    <div className="px-6 py-4 md:px-20">
      <FadeInSection>
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-semibold">Prescription Detail</h1>
          <Button variant="primary">
            <Download className="mr-2 h-4 w-4" />
            Download
          </Button>
        </div>

        <div className="w-full rounded-lg border border-gray-200 overflow-hidden">
          {mockData?.medications.map((medication, index) => (
            <div
              key={index}
              className={`grid md:grid-cols-2 gap-4 p-4 ${
                index !== mockData?.medications.length - 1
                  ? "border-b border-gray-200"
                  : ""
              }`}
            >
              <div>
                <p className="text-gray-700 text-base font-medium">
                  Drug Name:{" "}
                  <span className="text-gray-900 text-base font-semibold">
                    {medication.drugName}
                  </span>
                </p>

                <p className="text-gray-700 text-base font-medium">
                  Dosage:{" "}
                  <span className="text-gray-900 text-base font-semibold">
                    {medication.dosage}
                  </span>
                </p>

                <p className="text-gray-700 text-base font-medium">
                  Instruction:{" "}
                  <span className="text-gray-900 text-base font-semibold">
                    {medication.instruction}
                  </span>
                </p>
              </div>

              <div>
                <p className="text-gray-700 text-base font-medium">
                  Duration:{" "}
                  <span className="text-gray-900 text-base font-semibold">
                    {medication.duration}
                  </span>
                </p>

                <p className="text-gray-700 text-base font-medium">
                  Frequency:{" "}
                  <span className="text-gray-900 text-base font-semibold">
                    {medication.frequency}
                  </span>
                </p>
              </div>
            </div>
          ))}
        </div>

        <div className="mt-12 flex flex-col md:flex-row justify-between items-start md:items-center">
          <div>
            <p className="text-gray-900 text-base font-semibold">Signature</p>
            <div className="h-16">
              <SignaturePlaceholder />
            </div>
          </div>
          <div>
            <p className="text-gray-900 text-base font-semibold">Date</p>
            <p className="text-gray-900 text-base font-semibold">
              {mockData.date}
            </p>
          </div>
        </div>
      </FadeInSection>
    </div>
  );
};

export default PrescriptionDetails;
