import React from "react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { RadioGroup, RadioGroupItem } from "../../components/ui/radio-group-2";
import { Input } from "../../components/ui/input";
import { Button } from "../../components/ui/button";
import { Textarea } from "../../components/ui/textarea";
import { useToast } from "../../hooks/use-toast";
import { Loader2 } from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "../../components/ui/form";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "../../components/ui/accordion-availability";

const rosFormSchema = z.object({
  // General
  recentWeightChange: z.boolean().nullable().optional(),
  weakness: z.boolean().nullable().optional(),
  fatigue: z.boolean().nullable().optional(),
  fever: z.boolean().nullable().optional(),

  // HEENT - Head
  headInjury: z.boolean().nullable().optional(),
  dizziness: z.boolean().nullable().optional(),
  lightheadedness: z.boolean().nullable().optional(),

  // HEENT - Eyes
  visionChange: z.boolean().nullable().optional(),
  glassesOrContactLenses: z.boolean().nullable().optional(),
  eyePain: z.boolean().nullable().optional(),
  eyeRedness: z.boolean().nullable().optional(),
  excessiveTearing: z.boolean().nullable().optional(),
  doubleOrBlurredVision: z.boolean().nullable().optional(),
  spots: z.boolean().nullable().optional(),
  specks: z.boolean().nullable().optional(),
  flashingLights: z.boolean().nullable().optional(),
  glaucoma: z.boolean().nullable().optional(),
  cataracts: z.boolean().nullable().optional(),

  // HEENT - Ears
  hearingLoss: z.boolean().nullable().optional(),
  tinnitus: z.boolean().nullable().optional(),
  vertigo: z.boolean().nullable().optional(),
  earaches: z.boolean().nullable().optional(),
  earInfection: z.boolean().nullable().optional(),
  earDischarge: z.boolean().nullable().optional(),
  hearingAidsUse: z.boolean().nullable().optional(),

  // HEENT - Nose and Sinuses
  nasalDischarge: z.boolean().nullable().optional(),
  nasalItching: z.boolean().nullable().optional(),
  frequentColds: z.boolean().nullable().optional(),
  hayfever: z.boolean().nullable().optional(),
  nasalStuffiness: z.boolean().nullable().optional(),
  nosebleeds: z.boolean().nullable().optional(),
  sinusPressurePain: z.boolean().nullable().optional(),

  // HEENT - Throat (Mouth & Pharynx)
  dentalCondition: z.boolean().nullable().optional(),
  gumProblemsBleeding: z.boolean().nullable().optional(),
  dentures: z.boolean().nullable().optional(),
  denturesFit: z.string().optional(),
  lastDentalExam: z.string().optional(), // Changed to string to match form input
  soreTongue: z.boolean().nullable().optional(),
  dryMouth: z.boolean().nullable().optional(),
  frequentSoreThroats: z.boolean().nullable().optional(),
  hoarseness: z.boolean().nullable().optional(),

  // Neck
  swollenGlands: z.boolean().nullable().optional(),
  thyroidProblems: z.boolean().nullable().optional(),
  goiter: z.boolean().nullable().optional(),
  neckLumps: z.boolean().nullable().optional(),
  neckPainStiffness: z.boolean().nullable().optional(),

  // Breasts
  // breastSwollenGlands: z.boolean().nullable().optional(),
  breastLumps: z.boolean().nullable().optional(),
  breastPainDiscomfort: z.boolean().nullable().optional(),
  nippleDischarge: z.boolean().nullable().optional(),
  selfExamPractices: z.boolean().nullable().optional(),

  // Respiratory
  cough: z.boolean().nullable().optional(),
  sputum: z.boolean().nullable().optional(),
  sputumColor: z.string().optional(),
  sputumQuantity: z.string().optional(),
  sputumBlood: z.boolean().nullable().optional(),
  shortnessOfBreath_respiratory: z.boolean().nullable().optional(),
  wheezing: z.boolean().nullable().optional(),
  pleuriticPain: z.boolean().nullable().optional(),
  lastChestXray: z.string().optional(), // Changed to string to match form input
  asthma: z.boolean().nullable().optional(),
  bronchitis: z.boolean().nullable().optional(),
  emphysema: z.boolean().nullable().optional(),
  pneumonia: z.boolean().nullable().optional(),
  tuberculosis: z.boolean().nullable().optional(),

  // Cardiovascular
  heartTrouble: z.boolean().nullable().optional(),
  highBloodPressure: z.boolean().nullable().optional(),
  rheumaticFever: z.boolean().nullable().optional(),
  heartMurmurs: z.boolean().nullable().optional(),
  chestPain: z.boolean().nullable().optional(),
  palpitations: z.boolean().nullable().optional(),
  shortnessOfBreath_cardio: z.boolean().nullable().optional(),
  orthopnea: z.boolean().nullable().optional(),
  paroxysmalNocturnalDyspnea: z.boolean().nullable().optional(),
  edema: z.boolean().nullable().optional(),
  ekgOther: z.string().optional(),

  // Gastrointestinal
  unintentionalWeightChange: z.boolean().nullable().optional(),
  troubleSwallowing: z.boolean().nullable().optional(),
  heartburn: z.boolean().nullable().optional(),
  appetite: z.boolean().nullable().optional(),
  specialDiet: z.boolean().nullable().optional(),
  nausea: z.boolean().nullable().optional(),
  stoolColor: z.string().optional(),
  stoolSize: z.string().optional(),
  changeInBowelHabits: z.boolean().nullable().optional(),
  painWithDefecation: z.boolean().nullable().optional(),
  rectalBleeding: z.boolean().nullable().optional(),
  blackOrTarryStools: z.boolean().nullable().optional(),
  hemorrhoids: z.boolean().nullable().optional(),
  constipation: z.boolean().nullable().optional(),
  diarrhea: z.boolean().nullable().optional(),
  abdominalPain: z.boolean().nullable().optional(),
  foodIntolerance: z.boolean().nullable().optional(),
  excessiveBelching: z.boolean().nullable().optional(),
  jaundice: z.boolean().nullable().optional(),
  liverGallbladderTrouble: z.boolean().nullable().optional(),
  hepatitis: z.boolean().nullable().optional(),

  // Peripheral Vascular
  claudication: z.boolean().nullable().optional(),
  legCramps: z.boolean().nullable().optional(),
  varicoseVeins: z.boolean().nullable().optional(),
  pastClots: z.boolean().nullable().optional(),
  calfLegFeetSwelling: z.boolean().nullable().optional(),
  fingertipColorChangeCold: z.boolean().nullable().optional(),
  swellingRednessTendernessPeripheral: z.boolean().nullable().optional(),

  // Urinary
  frequencyOfUrination: z.boolean().nullable().optional(),
  polyuria: z.boolean().nullable().optional(),
  nocturia: z.boolean().nullable().optional(),
  urgency: z.boolean().nullable().optional(),
  burningPainDuringUrination: z.boolean().nullable().optional(),
  hematuria: z.boolean().nullable().optional(),
  urinaryInfections: z.boolean().nullable().optional(),
  kidneyOrFlankPain: z.boolean().nullable().optional(),
  kidneyStones: z.boolean().nullable().optional(),
  ureteralColic: z.boolean().nullable().optional(),
  suprapubicPain: z.boolean().nullable().optional(),
  incontinence: z.boolean().nullable().optional(),
  reducedUrinaryCaliberOrForce: z.boolean().nullable().optional(),
  hesitancy: z.boolean().nullable().optional(),
  dribbling: z.boolean().nullable().optional(),

  // Genital - Male
  hernias: z.boolean().nullable().optional(),
  penileDischarge: z.boolean().nullable().optional(),
  testicularPainOrMasses: z.boolean().nullable().optional(),
  scrotalPainOrSwelling: z.boolean().nullable().optional(),
  stdTreatment: z.string().optional(),
  sexualInterest: z.boolean().nullable().optional(),
  sexualFunction: z.boolean().nullable().optional(),
  sexualSatisfaction: z.boolean().nullable().optional(),
  birthControlMethodUse: z.boolean().nullable().optional(),
  condomUse: z.boolean().nullable().optional(),
  sexualProblems: z.boolean().nullable().optional(),
  hivInfectionConcerns: z.boolean().nullable().optional(),

  // Genital - Female
  ageAtMenarche: z.string().optional(),
  periodFrequency: z.string().optional(),
  durationOfPeriods: z.string().optional(),
  amountOfBleeding: z.string().optional(),
  periodRegularity: z.boolean().nullable().optional(),
  bleedingBetweenPeriods: z.boolean().nullable().optional(),
  bleedingAfterIntercourse: z.boolean().nullable().optional(),
  lastMenstrualPeriod: z.string().optional(), // Changed to string to match form input
  dysmenorrhea: z.boolean().nullable().optional(),
  premenstrualTension: z.boolean().nullable().optional(),
  ageAtMenopause: z.string().optional(),
  menopausalSymptoms: z.boolean().nullable().optional(),
  postMenopausalBleeding: z.boolean().nullable().optional(),
  vaginalDischarge: z.boolean().nullable().optional(),
  vaginalItching: z.boolean().nullable().optional(),
  vaginalScores: z.boolean().nullable().optional(),
  vaginalLumps: z.boolean().nullable().optional(),
  sexuallyTransmittedinfections: z.string().optional(),
  numberOfPregnancy: z.string().optional(),
  typeofDeliveries: z.string().optional(),
  numberOfAbortions: z.string().optional(),
  birthcontrolMethods: z.string().optional(),
  complicationsOfPregnancy: z.boolean().nullable().optional(),

  femaleSexualInterest: z.boolean().nullable().optional(),
  femaleSexualFunction: z.boolean().nullable().optional(),
  femaleSexualSatisfaction: z.boolean().nullable().optional(),
  femaleSexualDyspareunia: z.boolean().nullable().optional(),
  femaleSexualConcernsAboutHIVInfection: z.boolean().nullable().optional(),

  // Musculoskeletal
  muscleOrJointPain: z.boolean().nullable().optional(),
  stiffness: z.boolean().nullable().optional(),
  arthritis: z.boolean().nullable().optional(),
  gout: z.boolean().nullable().optional(),
  backache: z.boolean().nullable().optional(),
  muscleLocationDescription: z.string().optional(),
  swelling_muscle: z.boolean().nullable().optional(),
  redness_muscle: z.boolean().nullable().optional(),
  painMusculoskeletal: z.boolean().nullable().optional(),
  tenderness_muscle: z.boolean().nullable().optional(),
  weaknessMusculoskeletal: z.boolean().nullable().optional(),
  numbnessInLimb: z.boolean().nullable().optional(),
  limitationOfMotionOrActivity: z.boolean().nullable().optional(),
  timingMorning: z.boolean().nullable().optional(),
  timingEvening: z.string().optional(),
  timingOfSymptomsDetails: z.boolean().nullable().optional(),
  historyOfTrauma: z.boolean().nullable().optional(),
  neckOrLowBackPain: z.boolean().nullable().optional(),
  systemicJointPain: z.boolean().nullable().optional(),

  // Psychiatric
  nervousness: z.boolean().nullable().optional(),
  tension: z.boolean().nullable().optional(),
  feelingDownSadDepressed: z.boolean().nullable().optional(),
  memoryChange: z.boolean().nullable().optional(),
  suicidePlansOrAttempts: z.boolean().nullable().optional(),
  suicidalThoughts: z.boolean().nullable().optional(),
  pastCounseling: z.boolean().nullable().optional(),
  psychiatricAdmissions: z.boolean().nullable().optional(),
  onPsychiatricMedications: z.boolean().nullable().optional(),

  // Neurologic
  changeInMoodNeurologic: z.boolean().nullable().optional(),
  changeInAttention: z.boolean().nullable().optional(),
  changeInSpeech: z.boolean().nullable().optional(),
  changeInMemoryNeurologic: z.boolean().nullable().optional(),
  changeInInsight: z.boolean().nullable().optional(),
  changeInJudgement: z.boolean().nullable().optional(),
  headacheNeurologic: z.boolean().nullable().optional(),
  dizzinessNeurologic: z.boolean().nullable().optional(),
  vertigoNeurologic: z.boolean().nullable().optional(),
  fainting: z.boolean().nullable().optional(),
  blackouts: z.boolean().nullable().optional(),
  weaknessNeurologic: z.boolean().nullable().optional(),
  paralysis: z.boolean().nullable().optional(),
  numbnessOrLossOfSensation: z.boolean().nullable().optional(),
  tinglingPinsAndNeedles: z.boolean().nullable().optional(),
  tremorsOrOtherInvoluntaryMovement: z.boolean().nullable().optional(),
  seizures: z.boolean().nullable().optional(),

  // Hematologic
  anemia: z.boolean().nullable().optional(),
  easyBruisingOrBleeding: z.boolean().nullable().optional(),
  pastTransfusions: z.boolean().nullable().optional(),
  transfusionReactions: z.boolean().nullable().optional(),

  // Endocrine
  thyroidTrouble: z.boolean().nullable().optional(),
  heatOrColdIntolerance: z.boolean().nullable().optional(),
  excessiveSweating: z.boolean().nullable().optional(),
  excessiveThirstOrHunger: z.boolean().nullable().optional(),
  polyuriaEndocrine: z.boolean().nullable().optional(),
  changeInGloveOrShoeSize: z.boolean().nullable().optional(),
});

const ROS = () => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const form = useForm({
    resolver: zodResolver(rosFormSchema),
    defaultValues: {
      // General
      recentWeightChange: null,
      weakness: null,
      fatigue: null,
      fever: null,

      // HEENT - Head
      headInjury: null,
      dizziness: null,
      lightheadedness: null,

      // HEENT - Eyes
      visionChange: null,
      glassesOrContactLenses: null,
      eyePain: null,
      eyeRedness: null,
      excessiveTearing: null,
      doubleOrBlurredVision: null,
      spots: null,
      specks: null,
      flashingLights: null,
      glaucoma: null,
      cataracts: null,

      // HEENT - Ears
      hearingLoss: null,
      tinnitus: null,
      vertigo: null,
      earaches: null,
      earInfection: null,
      earDischarge: null,
      hearingAidsUse: null,

      // HEENT - Nose and Sinuses
      nasalDischarge: null,
      nasalItching: null,
      frequentColds: null,
      hayfever: null,
      nasalStuffiness: null,
      nosebleeds: null,
      sinusPressurePain: null,

      // HEENT - Throat (Mouth & Pharynx)
      dentalCondition: null,
      gumProblemsBleeding: null,
      dentures: null,
      denturesFit: "",
      lastDentalExam: "",
      soreTongue: null,
      dryMouth: null,
      frequentSoreThroats: null,
      hoarseness: null,

      // Neck
      swollenGlands: null,
      thyroidProblems: null,
      goiter: null,
      neckLumps: null,
      neckPainStiffness: null,

      // Breasts
      // breastSwollenGlands: null,
      breastLumps: null,
      breastPainDiscomfort: null,
      nippleDischarge: null,
      selfExamPractices: null,

      // Respiratory
      cough: null,
      sputum: null,
      sputumColor: "",
      sputumQuantity: "",
      sputumBlood: null,
      shortnessOfBreath_respiratory: null,
      wheezing: null,
      pleuriticPain: null,
      lastChestXray: "",
      asthma: null,
      bronchitis: null,
      emphysema: null,
      pneumonia: null,
      tuberculosis: null,

      // Cardiovascular
      heartTrouble: null,
      highBloodPressure: null,
      rheumaticFever: null,
      heartMurmurs: null,
      chestPain: null,
      palpitations: null,
      shortnessOfBreath_cardio: null,
      orthopnea: null,
      paroxysmalNocturnalDyspnea: null,
      edema: null,
      ekgOther: "",

      // Gastrointestinal
      unintentionalWeightChange: null,
      troubleSwallowing: null,
      heartburn: null,
      appetite: null,
      specialDiet: null,
      nausea: null,
      stoolColor: "",
      stoolSize: "",
      changeInBowelHabits: null,
      painWithDefecation: null,
      rectalBleeding: null,
      blackOrTarryStools: null,
      hemorrhoids: null,
      constipation: null,
      diarrhea: null,
      abdominalPain: null,
      foodIntolerance: null,
      excessiveBelching: null,
      jaundice: null,
      liverGallbladderTrouble: null,
      hepatitis: null,

      // Peripheral Vascular
      claudication: null,
      legCramps: null,
      varicoseVeins: null,
      pastClots: null,
      calfLegFeetSwelling: null,
      fingertipColorChangeCold: null,
      swellingRednessTendernessPeripheral: null,

      // Urinary
      frequencyOfUrination: null,
      polyuria: null,
      nocturia: null,
      urgency: null,
      burningPainDuringUrination: null,
      hematuria: null,
      urinaryInfections: null,
      kidneyOrFlankPain: null,
      kidneyStones: null,
      ureteralColic: null,
      suprapubicPain: null,
      incontinence: null,
      reducedUrinaryCaliberOrForce: null,
      hesitancy: null,
      dribbling: null,

      // Genital - Male
      hernias: null,
      penileDischarge: null,
      testicularPainOrMasses: null,
      scrotalPainOrSwelling: null,
      stdTreatment: "",
      sexualInterest: null,
      sexualFunction: null,
      sexualSatisfaction: null,
      birthControlMethodUse: null,
      condomUse: null,
      sexualProblems: null,
      hivInfectionConcerns: null,

      // Genital - Female
      ageAtMenarche: "",
      periodFrequency: "",
      durationOfPeriods: "",
      amountOfBleeding: "",
      periodRegularity: null,
      bleedingBetweenPeriods: null,
      bleedingAfterIntercourse: null,
      lastMenstrualPeriod: "",
      dysmenorrhea: null,
      premenstrualTension: null,
      ageAtMenopause: "",
      menopausalSymptoms: null,
      postMenopausalBleeding: null,
      vaginalDischarge: null,
      vaginalItching: null,
      vaginalScores: null,
      vaginalLumps: null,
      sexuallyTransmittedinfections: null,
      numberOfPregnancy: null,
      typeofDeliveries: null,
      numberOfAbortions: null,
      birthcontrolMethods: null,
      complicationsOfPregnancy: null,

      femaleSexualInterest: null,
      femaleSexualFunction: null,
      femaleSexualSatisfaction: null,
      femaleSexualDyspareunia: null,
      femaleSexualConcernsAboutHIVInfection: null,

      // Musculoskeletal
      muscleOrJointPain: null,
      stiffness: null,
      arthritis: null,
      gout: null,
      backache: null,
      muscleLocationDescription: "",
      swelling_muscle: null,
      redness_muscle: null,
      painMusculoskeletal: null,
      tenderness_muscle: null,
      weaknessMusculoskeletal: null,
      numbnessInLimb: null,
      limitationOfMotionOrActivity: null,
      timingMorning: null,
      timingEvening: null,
      timingEvening: null,
      historyOfTrauma: null,
      neckOrLowBackPain: null,
      systemicJointPain: null,

      // Psychiatric
      nervousness: null,
      tension: null,
      feelingDownSadDepressed: null,
      memoryChange: null,
      suicidePlansOrAttempts: null,
      suicidalThoughts: null,
      pastCounseling: null,
      psychiatricAdmissions: null,
      onPsychiatricMedications: null,

      // Neurologic
      changeInMoodNeurologic: null,
      changeInAttention: null,
      changeInSpeech: null,
      changeInMemoryNeurologic: null,
      changeInInsight: null,
      changeInJudgement: null,
      headacheNeurologic: null,
      dizzinessNeurologic: null,
      vertigoNeurologic: null,
      fainting: null,
      blackouts: null,
      weaknessNeurologic: null,
      paralysis: null,
      numbnessOrLossOfSensation: null,
      tinglingPinsAndNeedles: null,
      tremorsOrOtherInvoluntaryMovement: null,
      seizures: null,

      // Hematologic
      anemia: null,
      easyBruisingOrBleeding: null,
      pastTransfusions: null,
      transfusionReactions: null,

      // Endocrine
      thyroidTrouble: null,
      heatOrColdIntolerance: null,
      excessiveSweating: null,
      excessiveThirstOrHunger: null,
      polyuriaEndocrine: null,
      changeInGloveOrShoeSize: null,
    },
  });

  // Form submission handler
  const onSubmit = async (data) => {
    setIsSubmitting(true);
    try {
      await new Promise((resolve) => setTimeout(resolve, 1500));
      console.log("Form data submitted:", data);
      toast({
        title: "Success",
        description: "Review of Systems form has been submitted successfully.",
        variant: "default",
      });
    } catch (error) {
      console.error("Error submitting form:", error);
      toast({
        title: "Error",
        description:
          "There was a problem submitting the form. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderRadioField = (name, label) => {
    return (
      <div className="w-full grid grid-cols-4 gap-4 items-center">
        <FormField
          control={form.control}
          name={name}
          render={({ field }) => (
            <>
              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormLabel
                  id={`${name}-label`}
                  className="text-[16px] font-[600]"
                  title="Click the same option again to unselect"
                >
                  {label}
                </FormLabel>
              </FormItem>

              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormControl>
                  <RadioGroup
                    onValueChange={(value) => {
                      const newValue =
                        value === "yes" ? true : value === "no" ? false : null;
                      if (
                        (value === "yes" && field.value === true) ||
                        (value === "no" && field.value === false)
                      ) {
                        field.onChange(null);
                      } else {
                        field.onChange(newValue);
                      }
                    }}
                    value={
                      field.value === true
                        ? "yes"
                        : field.value === false
                          ? "no"
                          : ""
                    }
                    className="w-full grid grid-cols-1 md:grid-cols-2 gap-2"
                    aria-labelledby={`${name}-label`}
                  >
                    <FormItem className="col-span-1 flex items-center space-x-2 space-y-0 border border-[#E7E8E9] px-8 py-4 rounded-lg hover:bg-gray-50">
                      <FormControl>
                        <RadioGroupItem
                          value="yes"
                          id={`${name}-yes`}
                          onClick={() => {
                            if (field.value === true) {
                              field.onChange(null);
                            }
                          }}
                        />
                      </FormControl>
                      <FormLabel
                        htmlFor={`${name}-yes`}
                        className="text-[16px] font-[600] leading-none cursor-pointer"
                      >
                        Yes
                      </FormLabel>
                    </FormItem>

                    <FormItem className="col-span-1 flex items-center space-x-2 space-y-0 border border-[#E7E8E9] px-4 py-2 rounded-lg hover:bg-gray-50">
                      <FormControl>
                        <RadioGroupItem
                          value="no"
                          id={`${name}-no`}
                          onClick={() => {
                            if (field.value === false) {
                              field.onChange(null);
                            }
                          }}
                        />
                      </FormControl>
                      <FormLabel
                        htmlFor={`${name}-no`}
                        className="text-[16px] font-[600] leading-none cursor-pointer"
                      >
                        No
                      </FormLabel>
                    </FormItem>
                  </RadioGroup>
                </FormControl>
                <FormMessage />
              </FormItem>
            </>
          )}
        />
      </div>
    );
  };
  // Function to create an input field
  const renderInputField = (name, label, placeholder = "Enter value") => {
    return (
      <div className="w-full grid grid-cols-4 gap-4 items-center">
        <FormField
          control={form.control}
          name={name}
          render={({ field }) => (
            <>
              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormLabel className="text-[16px] font-[600] px-2">
                  {label}
                </FormLabel>
              </FormItem>
              <FormItem className="col-span-2">
                <FormControl>
                  <Input id={name} placeholder={placeholder} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            </>
          )}
        />
      </div>
    );
  };

  // Function to create a textarea field
  const renderTextareaField = (name, label, placeholder = "Enter notes") => {
    return (
      <div className="w-full grid grid-cols-4 gap-4 items-center">
        <FormField
          control={form.control}
          name={name}
          render={({ field }) => (
            <>
              <FormItem className="col-span-2 flex items-center space-y-0">
                <FormLabel className="text-[16px] font-[600] px-2">
                  {label}
                </FormLabel>
              </FormItem>
              <FormItem className="col-span-2">
                <FormControl>
                  <Textarea id={name} placeholder={placeholder} {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            </>
          )}
        />
      </div>
    );
  };

  return (
    <div className="container mx-auto">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="">
          <Accordion type="multiple" className="w-full space-y-4">
            {/* General */}
            <AccordionItem value="general" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">General</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField(
                    "recentWeightChange",
                    "Recent Weight Change",
                  )}
                  {renderRadioField("weakness", "Weakness")}
                  {renderRadioField("fatigue", "Fatigue")}
                  {renderRadioField("fever", "Fever")}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* HEENT */}
            <AccordionItem value="heent-head" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">HEENT</h3>
              </AccordionTrigger>
              <AccordionContent className="">
                <div className="space-y-6 px-4">
                  <div className="space-y-4">
                    <h2 className="text-xl font-semibold my-4">Head</h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioField("headInjury", "Head Injury")}
                      {renderRadioField("dizziness", "Dizziness")}
                      {renderRadioField("lightheadedness", "Lightheadedness")}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold my-4">Eyes</h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioField("visionChange", "Vision Change")}
                      {renderRadioField(
                        "glassesOrContactLenses",
                        "Glasses or Contact Lenses",
                      )}
                      {renderRadioField("eyePain", "Eye Pain")}
                      {renderRadioField("eyeRedness", "Eye Redness")}
                      {renderRadioField(
                        "excessiveTearing",
                        "Excessive Tearing",
                      )}
                      {renderRadioField(
                        "doubleOrBlurredVision",
                        "Double or Blurred Vision",
                      )}
                      {renderRadioField("spots", "Spots")}
                      {renderRadioField("specks", "Specks")}
                      {renderRadioField("flashingLights", "Flashing Lights")}
                      {renderRadioField("glaucoma", "Glaucoma")}
                      {renderRadioField("cataracts", "Cataracts")}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold my-4">Ears</h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioField("hearingLoss", "Hearing Loss")}
                      {renderRadioField(
                        "tinnitus",
                        "Ringing in Ear(s)/Tinnitus",
                      )}
                      {renderRadioField("vertigo", "Vertigo")}
                      {renderRadioField("earaches", "Earaches")}
                      {renderRadioField("earInfection", "Infection")}
                      {renderRadioField("earDischarge", "Discharge")}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-base font-semibold">
                      If hearing is decreased
                    </h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioField(
                        "hearingAidsUse",
                        "Use/non-use of hearing aids ",
                      )}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold my-4">
                      Nose and Sinuses
                    </h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioField("nasalDischarge", "Discharge")}
                      {renderRadioField("nasalItching", "Itching")}
                      {renderRadioField("frequentColds", "Frequent Colds")}
                      {renderRadioField("hayfever", "Hayfever")}
                      {renderRadioField("nasalStuffiness", "Nasal Stuffiness")}
                      {renderRadioField("nosebleeds", "Nosebleeds")}
                      {renderRadioField(
                        "sinusPressurePain",
                        "Sinus pressure/pain",
                      )}
                    </div>
                  </div>

                  <div>
                    <h2 className="text-xl font-semibold mb-4">
                      Throat (Mouth & Pharynx)
                    </h2>
                    <div className="grid grid-cols-1 gap-2">
                      {renderRadioField(
                        "dentalCondition",
                        "Condition of teeth and gum",
                      )}
                      {renderRadioField(
                        "gumProblemsBleeding",
                        "Gum Problems/ Bleeding",
                      )}
                      {renderRadioField("dentures", "Dentures (if any)")}
                      {renderInputField(
                        "denturesFit",
                        "Fit of Dentures",
                        "Describe fit of dentures",
                      )}
                      {renderInputField(
                        "lastDentalExam",
                        "Last Dental Exam",
                        "Enter date of last dental exam",
                      )}
                      {renderRadioField("soreTongue", "Sore Tongue")}
                      {renderRadioField("dryMouth", "Dry Mouth")}
                      {renderRadioField(
                        "frequentSoreThroats",
                        "Frequent Sore Throats",
                      )}
                      {renderRadioField("hoarseness", "Hoarseness")}
                    </div>
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Neck */}
            <AccordionItem value="neck" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Neck</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField("swollenGlands", "Swollen Glands")}
                  {renderRadioField("thyroidProblems", "Thyroid Problems")}
                  {renderRadioField("goiter", "Goiter")}
                  {renderRadioField("neckLumps", "Lumps")}
                  {renderRadioField("neckPainStiffness", "Pain or Stiffness")}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Breasts */}
            <AccordionItem value="breasts" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Breasts</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField("breastLumps", "Lumps")}
                  {renderRadioField(
                    "breastPainDiscomfort",
                    "Pain or discomfort",
                  )}
                  {renderRadioField("nippleDischarge", "Nipple discharge")}
                  {renderRadioField("selfExamPractices", "Self-exam practices")}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Respiratory */}
            <AccordionItem value="respiratory" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Respiratory</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField("cough", "Cough")}
                  {renderRadioField("sputum", "Sputum")}

                  <h3 className="text-base font-semibold">
                    If yes, provide sputum details
                  </h3>
                  {renderInputField(
                    "sputumColor",
                    "Sputum Color",
                    "Describe sputum color",
                  )}
                  {renderInputField(
                    "sputumQuantity",
                    "Sputum Quantity",
                    "Describe sputum quantity",
                  )}
                  {renderRadioField("sputumBlood", "Presence of blood")}
                  {renderRadioField(
                    "shortnessOfBreath_respiratory",
                    "Shortness of breath (Dyspnea)",
                  )}
                  {renderRadioField("wheezing", "Wheezing")}
                  {renderRadioField(
                    "pleuriticPain",
                    "Pain with a deep breath (Pleuritic pain)",
                  )}
                  {renderInputField(
                    "lastChestXray",
                    "Last Chest X-Ray",
                    "Enter date of last chest X-ray",
                  )}
                  {renderRadioField("asthma", "Asthma")}
                  {renderRadioField("bronchitis", "Bronchitis")}
                  {renderRadioField("emphysema", "Emphysema")}
                  {renderRadioField("pneumonia", "Pneumonia")}
                  {renderRadioField("tuberculosis", "Tuberculosis")}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Cardiovascular */}
            <AccordionItem value="cardiovascular" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Cardiovascular</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField("heartTrouble", "Heart Trouble")}
                  {renderRadioField("highBloodPressure", "High Blood Pressure")}
                  {renderRadioField("rheumaticFever", "Rheumatic Fever")}
                  {renderRadioField("heartMurmurs", "Heart Murmurs")}
                  {renderRadioField("chestPain", "Chest pain or discomfort")}
                  {renderRadioField("palpitations", "Palpitations")}
                  {renderRadioField(
                    "shortnessOfBreath_cardio",
                    "Shortness of Breath",
                  )}
                  {renderRadioField(
                    "orthopnea",
                    "Use of pillows or head of bed elevation at night to ease breathing (Orthopnea)",
                  )}
                  {renderRadioField(
                    "paroxysmalNocturnalDyspnea",
                    "Need to sit up at night to ease breathing (paroxysmal nocturnal dyspnea)",
                  )}
                  {renderRadioField(
                    "edema",
                    "Swelling in the hands, ankles or feet (edema)",
                  )}
                  {renderInputField(
                    "ekgOther",
                    "Results of past EKG or other Cardiovascular tests EKG/ Other:",
                    "Value",
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Gastrointestinal */}
            <AccordionItem
              value="gastrointestinal"
              className="border rounded-lg"
            >
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Gastrointestinal</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField(
                    "unintentionalWeightChange",
                    "Unintentional Weight loss/ gain",
                  )}
                  {renderRadioField("troubleSwallowing", "Trouble swallowing")}
                  {renderRadioField("heartburn", "Heartburn")}
                  {renderRadioField("appetite", "Appetite")}
                  {renderRadioField("specialDiet", "On Special Diet")}
                  {renderRadioField("nausea", "Nausea")}

                  <h3 className="text-base font-semibold">Bowel Movements</h3>

                  {renderInputField(
                    "stoolColor",
                    "Stool Color",
                    "Describe stool color",
                  )}
                  {renderInputField(
                    "stoolSize",
                    "Stool Size",
                    "Describe stool size",
                  )}
                  {renderRadioField(
                    "changeInBowelHabits",
                    "Change in Bowel Habits",
                  )}
                  {renderRadioField(
                    "painWithDefecation",
                    "Pain with Defecation",
                  )}
                  {renderRadioField("rectalBleeding", "Rectal Bleeding")}
                  {renderRadioField(
                    "blackOrTarryStools",
                    "Black or Tarry Stools",
                  )}
                  {renderRadioField("hemorrhoids", "Hemorrhoids")}
                  {renderRadioField("constipation", "Constipation")}
                  {renderRadioField(
                    "diarrhea",
                    "Diarrhea/Irritable Bowel Syndrome",
                  )}
                  {renderRadioField("abdominalPain", "Abdominal Pain")}
                  {renderRadioField("foodIntolerance", "Food Intolerance")}
                  {renderRadioField(
                    "excessiveBelching",
                    "Excessive belching or passing of gas",
                  )}
                  {renderRadioField("jaundice", "Jaundice (yellowing of skin)")}
                  {renderRadioField(
                    "liverGallbladderTrouble",
                    "Liver or Gallbladder Trouble",
                  )}
                  {renderRadioField("hepatitis", "Hepatitis")}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Peripheral Vascular */}
            <AccordionItem
              value="peripheral-vascular"
              className="border rounded-lg"
            >
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Peripheral Vascular</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField(
                    "claudication",
                    "Intermittent leg pain with exertion (claudication)",
                  )}
                  {renderRadioField("legCramps", "Leg Cramps")}
                  {renderRadioField("varicoseVeins", "Varicose Veins")}
                  {renderRadioField("pastClots", "Past clots in the veins")}
                  {renderRadioField(
                    "calfLegFeetSwelling",
                    "Swelling in the calves, legs, or feet",
                  )}
                  {renderRadioField(
                    "fingertipColorChangeCold",
                    "Color change in fingertips/toes during cold weather",
                  )}
                  {renderRadioField(
                    "swellingRednessTendernessPeripheral",
                    "Swelling with redness/tenderness",
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Urinary */}
            <AccordionItem value="urinary" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Urinary</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField(
                    "frequencyOfUrination",
                    "Frequency of Urination",
                  )}
                  {renderRadioField("polyuria", "Polyuria")}
                  {renderRadioField("nocturia", "Nocturia")}
                  {renderRadioField("urgency", "Urgency")}
                  {renderRadioField(
                    "burningPainDuringUrination",
                    "Burning or Pain During Urination",
                  )}
                  {renderRadioField("hematuria", "Hematuria")}
                  {renderRadioField(
                    "urinaryInfections",
                    "Blood in the urine (hematuria)",
                  )}
                  {renderRadioField("kidneyOrFlankPain", "Urinary infections")}
                  {renderRadioField("kidneyStones", "Kidney Stones")}
                  {renderRadioField("ureteralColic", "Ureteral Colic")}
                  {renderRadioField("suprapubicPain", "Suprapubic Pain")}
                  {renderRadioField("incontinence", "Incontinence")}
                  {renderRadioField(
                    "reducedUrinaryCaliberOrForce",
                    "Reduced caliber or force of the urinary system",
                  )}
                  {renderRadioField("hesitancy", "Hesitancy")}
                  {renderRadioField("dribbling", "Dribbling")}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Genital (Male) */}
            <AccordionItem value="genital-male" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Genital (Male)</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField("hernias", "Hernias")}
                  {renderRadioField(
                    "penileDischarge",
                    "Discharge from sores on the penis",
                  )}
                  {renderRadioField(
                    "testicularPainOrMasses",
                    "Testicular Pain or Masses",
                  )}
                  {renderRadioField(
                    "scrotalPainOrSwelling",
                    "Scrotal Pain or Swelling",
                  )}
                  <h3 className="text-base font-semibold">
                    History of Sexually Transmitted infections If yes,
                  </h3>

                  {renderInputField(
                    "stdTreatment",
                    "Treatment",
                    "Describe STD treatment",
                  )}

                  <h3 className="text-base font-semibold">Sexual Habits</h3>
                  {renderRadioField("sexualInterest", "Interest")}
                  {renderRadioField("sexualFunction", "Function")}
                  {renderRadioField("sexualSatisfaction", "Satisfaction")}
                  {renderRadioField(
                    "birthControlMethodUse",
                    "Birth Control Methods",
                  )}
                  {renderRadioField("condomUse", "Condom Use")}
                  {renderRadioField("sexualProblems", "Problems")}
                  {renderRadioField(
                    "hivInfectionConcerns",
                    "Concerns about HIV Infection ",
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Genital (Female) */}
            <AccordionItem value="genital-female" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Genital (Female)</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderInputField(
                    "ageAtMenarche",
                    "Age at Menarche",
                    "Enter age",
                  )}
                  {renderInputField(
                    "periodFrequency",
                    "Period Frequency",
                    "Enter frequency",
                  )}
                  {renderInputField(
                    "durationOfPeriods",
                    "Duration of Periods",
                    "Enter duration",
                  )}
                  {renderInputField(
                    "amountOfBleeding",
                    "Amount of Bleeding",
                    "Describe amount",
                  )}
                  {renderRadioField("periodRegularity", "Period Regularity")}
                  {renderRadioField(
                    "bleedingBetweenPeriods",
                    "Bleeding Between Periods",
                  )}
                  {renderRadioField(
                    "bleedingAfterIntercourse",
                    "Bleeding After Intercourse",
                  )}
                  {renderInputField(
                    "lastMenstrualPeriod",
                    "Last Menstrual Period",
                    "Enter date",
                  )}
                  {renderRadioField("dysmenorrhea", "Dysmenorrhea")}
                  {renderRadioField(
                    "premenstrualTension",
                    "Premenstrual Tension",
                  )}
                  {renderInputField(
                    "ageAtMenopause",
                    "Age at Menopause",
                    "Enter age",
                  )}
                  {renderRadioField(
                    "menopausalSymptoms",
                    "Menopausal Symptoms",
                  )}
                  {renderRadioField(
                    "postMenopausalBleeding",
                    "Post-Menopausal Bleeding",
                  )}
                  {renderRadioField("vaginalDischarge", "Vaginal Discharge")}
                  {renderRadioField("vaginalItching", "Itching")}
                  {renderRadioField("vaginalScores", "Sores")}
                  {renderRadioField("vaginalLumps", "Lumps")}
                  <h3 className="text-base font-semibold">
                    History of Sexually Transmitted infections, If yes
                  </h3>
                  {renderInputField(
                    "sexuallyTransmittedinfections",
                    "Treatment",
                    "value",
                  )}
                  {renderInputField(
                    "numberOfPregnancy",
                    "Number of pregnancies",
                    "value",
                  )}
                  {renderInputField(
                    "typeofDeliveries",
                    "Type of pregnancies",
                    "value",
                  )}
                  {renderInputField(
                    "numberOfAbortions",
                    "Number of Abortions",
                    "value",
                  )}
                  {renderInputField(
                    "birthcontrolMethods",
                    "Birth Control Methods",
                    "value",
                  )}

                  {renderRadioField(
                    "complicationsOfPregnancy",
                    "Complications of pregnancy",
                  )}

                  <h3 className="text-base font-semibold">Sexual Habits</h3>
                  {renderRadioField("femaleSexualInterest", "Interest")}
                  {renderRadioField("femaleSexualFunction", "Function")}
                  {renderRadioField("femaleSexualSatisfaction", "Satisfaction")}
                  {renderRadioField(
                    "femaleSexualDyspareunia",
                    "Any problems including dyspareunia",
                  )}
                  {renderRadioField(
                    "femaleSexualConcernsAboutHIVInfection",
                    "Concerns about HIV infection",
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Musculoskeletal */}
            <AccordionItem
              value="musculoskeletal"
              className="border rounded-lg"
            >
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Musculoskeletal</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField(
                    "muscleOrJointPain",
                    "Muscle or joint pain",
                  )}
                  {renderRadioField("stiffness", "Stiffness")}
                  {renderRadioField("arthritis", "Arthritis")}
                  {renderRadioField("gout", "Gout")}
                  {renderRadioField("backache", "Backache")}
                  {renderInputField(
                    "muscleLocationDescription",
                    "If present, describe location of affected joints/muscles:",
                    "Describe affected areas",
                  )}
                  {renderRadioField("swelling_muscle", "Swelling")}
                  {renderRadioField("redness_muscle", "Redness")}
                  {renderRadioField("painMusculoskeletal", "Pain")}
                  {renderRadioField("tenderness_muscle", "Tenderness")}
                  {renderRadioField("weaknessMusculoskeletal", "Weakness")}
                  {renderRadioField("numbnessInLimb", "Numbness in Limb")}
                  {renderRadioField(
                    "limitationOfMotionOrActivity",
                    "Limitation of Motion or Activity",
                  )}

                  <h3 className="text-base font-semibold">
                    Timing of Symptoms
                  </h3>

                  {renderRadioField("timingMorning", "Morning")}
                  {renderRadioField("timingEvening", "Evening")}
                  {renderInputField("timingEvening", "Details", "value")}

                  {renderRadioField("historyOfTrauma", "History of Trauma")}
                  {renderRadioField(
                    "neckOrLowBackPain",
                    "Neck or Low Back Pain",
                  )}
                  {renderRadioField(
                    "systemicJointPain",
                    "Joint pain with systemic symptoms such as fever, chills, rash, anorexia, weight loss or weakness",
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Psychiatric  */}
            <AccordionItem value="psychiatric" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Psychiatric</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField("nervousness", "Nervousness")}
                  {renderRadioField("tension", "Tension")}

                  <h3 className="text-base font-semibold">Mood</h3>

                  {renderRadioField(
                    "feelingDownSadDepressed",
                    "Feeling Down, Sad, or Depressed",
                  )}
                  {renderRadioField("memoryChange", "Memory Change")}
                  {renderRadioField(
                    "suicidePlansOrAttempts",
                    "Suicide Plans or Attempts",
                  )}
                  {renderRadioField("suicidalThoughts", "Suicidal Thoughts")}
                  {renderRadioField("pastCounseling", "Past Counseling")}
                  {renderRadioField(
                    "psychiatricAdmissions",
                    "Psychiatric Admissions",
                  )}
                  {renderRadioField(
                    "onPsychiatricMedications",
                    "On Psychiatric Medications",
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Neurologic */}
            <AccordionItem value="neurologic" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Neurologic</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField("changeInMoodNeurologic", "Change in Mood")}
                  {renderRadioField("changeInAttention", "Change in Attention")}
                  {renderRadioField("changeInSpeech", "Change in Speech")}
                  {renderRadioField(
                    "changeInMemoryNeurologic",
                    "Change in Memory",
                  )}
                  {renderRadioField("changeInInsight", "Change in Insight")}
                  {renderRadioField("changeInJudgement", "Change in Judgement")}
                  {renderRadioField("headacheNeurologic", "Headache")}
                  {renderRadioField("dizzinessNeurologic", "Dizziness")}
                  {renderRadioField("vertigoNeurologic", "Vertigo")}
                  {renderRadioField("fainting", "Fainting")}
                  {renderRadioField("blackouts", "Blackouts")}
                  {renderRadioField("weaknessNeurologic", "Weakness")}
                  {renderRadioField("paralysis", "Paralysis")}
                  {renderRadioField(
                    "numbnessOrLossOfSensation",
                    "Numbness or Loss of Sensation",
                  )}
                  {renderRadioField(
                    "tinglingPinsAndNeedles",
                    "Tingling or Pins and Needles",
                  )}
                  {renderRadioField(
                    "tremorsOrOtherInvoluntaryMovement",
                    "Tremors or Other involuntary movement",
                  )}
                  {renderRadioField("seizures", "Seizures")}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Hematologic */}
            <AccordionItem value="hematologic" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Hematologic</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField("anemia", "Anemia")}
                  {renderRadioField(
                    "easyBruisingOrBleeding",
                    "Easy bruising or bleeding",
                  )}
                  {renderRadioField("pastTransfusions", "Past transfusions")}
                  {renderRadioField(
                    "transfusionReactions",
                    "Transfusion reactions",
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Endocrine */}
            <AccordionItem value="endocrine" className="border rounded-lg">
              <AccordionTrigger className="px-4 py-2 hover:bg-gray-50 rounded-t-lg">
                <h3 className="text-xl font-bold">Endocrine</h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-4 space-y-2">
                <div className="grid grid-cols-1 gap-2">
                  {renderRadioField("thyroidTrouble", "Thyroid trouble")}
                  {renderRadioField(
                    "heatOrColdIntolerance",
                    "Heat or Cold Intolerance",
                  )}
                  {renderRadioField("excessiveSweating", "Excessive Sweating")}
                  {renderRadioField(
                    "excessiveThirstOrHunger",
                    "Excessive thirst or hunger",
                  )}
                  {renderRadioField("polyuriaEndocrine", "Polyuria")}
                  {renderRadioField(
                    "changeInGloveOrShoeSize",
                    "Change in glove or shoe size",
                  )}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <div className="flex justify-end gap-4 py-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => form.reset()}
              disabled={isSubmitting}
            >
              Reset
            </Button>
            <Button type="submit" disabled={isSubmitting} variant="primary">
              {isSubmitting ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Submitting...
                </>
              ) : (
                "Submit"
              )}
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};
export default ROS;
