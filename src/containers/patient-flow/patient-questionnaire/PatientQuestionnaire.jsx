"use client";
import { useState, useEffect, useRef, useCallback } from "react";
import { <PERSON>evronLeft, Loader2 } from "lucide-react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Form } from "../../../components/ui/form";
import { Button } from "../../../components/ui/button";
import { useToast } from "../../../hooks/use-toast";
import { useNavigate, useLocation } from "react-router-dom";
import { createPreAppointmentForm_apiCalls } from "../../../api/api_calls/appointment_apiCalls";
import Step1Form from "./components/Step1TodaysVisit";
import Step2Form from "./components/Step2BetweenVisits";
import Step3Form from "./components/Step3Lifestyle";
import Step4Form from "./components/Step4Update";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogTitle,
} from "../../../components/ui/dialog";
import CheckCircle from "../../../assets/images/Check.png";

const PatientQuestionnaireSchema = z.object({
  // Required appointmentId
  appointmentId: z.string().min(1, "Appointment ID is required"),

  // Step 1 - Today's Visit
  goalForHealth: z.string().optional(),
  agendaForAppointment: z.string().optional(),
  isMedCertificateRequested: z.boolean().optional().default(false),

  // Diabetes
  diabetes_isProblemWithMedication: z.boolean().nullable().optional(),
  diabetes_homeGlucoseReadings: z.string().optional().default(""),

  // High BP
  highBloodPressure_isProblemWithMedication: z.boolean().nullable().optional(),
  highBloodPressure_homeBpReadings: z.string().optional().default(""),

  // Depression
  depression_isProblemWithMedication: z.boolean().nullable().optional(),
  depression_isSuicidalThoughts: z.boolean().nullable().optional(),

  // High Cholesterol
  highCholesterol_isProblemWithMedication: z.boolean().nullable().optional(),

  // Step 2 - Between Visits
  otherMedVisit: z.boolean().nullable().optional(),
  otherMedVisit_details: z.string().optional().default(""),

  // Step 3 - Lifestyle
  exercice: z.string().optional().default(""),
  exercice_howLong: z.string().optional().default(""),
  exercice_howOften: z.string().optional().default(""),
  isShortnessOfBreath: z.boolean().nullable().optional(),

  // Smoking
  smoking_isInterestedInQuitting: z.boolean().nullable().optional(),
  smoking_howMuch: z.string().optional().default(""),

  // Alcohol
  alcohol_numberOfDrinks_week: z.string().optional().default(""),
  alcohol_numberOfDrinks_day: z.string().optional().default(""),
  alcohol_moreThanFourDrinks: z.boolean().nullable().optional(),
  alcohol_othersConcerned: z.boolean().nullable().optional(),

  // Falls
  fallen: z.boolean().nullable().optional(),
  balanceProblems: z.boolean().nullable().optional(),

  // Caffeine
  caffeinePerDay: z.string().optional().default(""),

  // Birth Control
  birthControlMethod: z.string().optional().default(""),

  // Safety
  isUnsafeRelationship: z.boolean().nullable().optional(),
  isWearSeatbelt: z.boolean().nullable().optional(),

  // HIV
  isHivTestRequested: z.boolean().nullable().optional(),

  // Sleep
  isSleepApnea: z.boolean().nullable().optional(),

  // Depression Screen
  isFeelingDown: z.boolean().nullable().optional(),

  // Medication
  isProblemWithMedication: z.boolean().nullable().optional(),
  problemWithMedicationDetails: z.string().optional().default(""),

  // Bladder Control
  isProblemBladderControl: z.boolean().nullable().optional(),

  // End of Life Care
  discussEndOfLifeCare: z.boolean().nullable().optional(),

  // Step 4 - Update
  newIllnessInFamily: z.string().optional().default(""),
  newDrugAllergies: z.string().optional().default(""),
  specificIssues: z.string().optional().default(""),

  // Symptoms (arrays matching backend enums)
  constitutionalSymptoms: z.array(z.string()).optional().default([]),
  eyeProblems: z.array(z.string()).optional().default([]),
  enmtProblems: z.array(z.string()).optional().default([]),
  cardiovascularProblems: z.array(z.string()).optional().default([]),
  respiratoryProblems: z.array(z.string()).optional().default([]),
  gastrointestinalProblems: z.array(z.string()).optional().default([]),
  genitourinaryProblems: z.array(z.string()).optional().default([]),
  skinProblems: z.array(z.string()).optional().default([]),
  sleepProblems: z.array(z.string()).optional().default([]),
  neurologicalProblems: z.array(z.string()).optional().default([]),
  musculoskeletalProblems: z.array(z.string()).optional().default([]),
  psychiatricProblems: z.array(z.string()).optional().default([]),
  endocrineProblems: z.array(z.string()).optional().default([]),
  hematologicProblems: z.array(z.string()).optional().default([]),
  allergicProblems: z.array(z.string()).optional().default([]),
});

const PatientQuestionnaire = () => {
  const [activeStep, setActiveStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();
  const firstInputRef = useRef(null);
  const navigate = useNavigate();
  const location = useLocation();
  const [isSuccessDialogOpen, setIsSuccessDialogOpen] = useState(false);

  // Get appointmentId from navigation state
  const appointmentId = location.state?.appointmentId;
  console.log("appointmentId :", appointmentId);

  const form = useForm({
    resolver: zodResolver(PatientQuestionnaireSchema),
    defaultValues: {
      // Required appointmentId
      appointmentId: appointmentId || "",

      // Step 1 - Today's Visit
      goalForHealth: "",
      agendaForAppointment: "",
      isMedCertificateRequested: false,
      diabetes_isProblemWithMedication: null,
      diabetes_homeGlucoseReadings: "",
      highBloodPressure_isProblemWithMedication: null,
      highBloodPressure_homeBpReadings: "",
      depression_isProblemWithMedication: null,
      depression_isSuicidalThoughts: null,
      highCholesterol_isProblemWithMedication: null,

      // Step 2 - Between Visits
      otherMedVisit: null,
      otherMedVisit_details: "",

      // Step 3 - Lifestyle
      exercice: "",
      exercice_howLong: "",
      exercice_howOften: "",
      isShortnessOfBreath: null,
      smoking_isInterestedInQuitting: null,
      smoking_howMuch: "",
      alcohol_numberOfDrinks_week: "",
      alcohol_numberOfDrinks_day: "",
      alcohol_moreThanFourDrinks: null,
      alcohol_othersConcerned: null,
      fallen: null,
      balanceProblems: null,
      caffeinePerDay: "",
      birthControlMethod: "",
      isUnsafeRelationship: null,
      isWearSeatbelt: null,
      isHivTestRequested: null,
      isSleepApnea: null,
      isFeelingDown: null,
      isProblemWithMedication: null,
      problemWithMedicationDetails: "",
      isProblemBladderControl: null,
      discussEndOfLifeCare: null,

      // Step 4 - Update
      newIllnessInFamily: "",
      newDrugAllergies: "",
      specificIssues: "",
      constitutionalSymptoms: [],
      eyeProblems: [],
      enmtProblems: [],
      cardiovascularProblems: [],
      respiratoryProblems: [],
      gastrointestinalProblems: [],
      genitourinaryProblems: [],
      skinProblems: [],
      sleepProblems: [],
      neurologicalProblems: [],
      musculoskeletalProblems: [],
      psychiatricProblems: [],
      endocrineProblems: [],
      hematologicProblems: [],
      allergicProblems: [],
    },
    mode: "onChange",
  });

  // Auto-focus first input on step change
  useEffect(() => {
    if (firstInputRef.current) {
      setTimeout(() => {
        firstInputRef.current.focus();
      }, 100);
    }
  }, [activeStep]);

  // Check if appointmentId is available
  useEffect(() => {
    if (!appointmentId) {
      toast({
        title: "Missing Appointment Information",
        description:
          "Please book an appointment first before filling the questionnaire.",
        variant: "destructive",
      });
      // Redirect to find doctor page after 3 seconds
      setTimeout(() => {
        navigate("/patient/find-doctor");
      }, 3000);
    }
  }, [appointmentId, toast, navigate]);

  // Get field validation schema for the current step
  const getFieldsToValidate = useCallback(() => {
    switch (activeStep) {
      case 1:
        return ["goalForHealth"];
      case 2:
        return ["otherMedVisit"];
      case 3:
        return [];
      case 4:
        return [];
      default:
        return [];
    }
  }, [activeStep]);

  const handleNext = useCallback(async () => {
    try {
      // Validate only the fields for the current step
      const fieldsToValidate = getFieldsToValidate();
      const result = await form.trigger(fieldsToValidate);

      if (result) {
        // Move to next step if validation passes
        if (activeStep < 4) {
          setActiveStep(activeStep + 1);
          // Scroll to top when changing steps
          window.scrollTo(0, 0);
        }
      } else {
        // Show toast for validation errors
        toast({
          title: "Validation Error",
          description: "Please fill in all required fields correctly.",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Validation error:", error);
    }
  }, [activeStep, form, getFieldsToValidate, toast]);

  const handlePrevious = useCallback(() => {
    // Move to previous step
    setActiveStep(activeStep - 1);
    // Scroll to top when changing steps
    window.scrollTo(0, 0);
  }, [activeStep]);

  const onSubmit = useCallback(
    async (values) => {
      setIsSubmitting(true);
      try {
        // Validate appointmentId
        if (!values.appointmentId) {
          throw new Error("Appointment ID is required");
        }

        // Transform null values to false for boolean fields (backend compatibility)
        const transformedValues = {
          ...values,
          // Step 1 boolean fields
          diabetes_isProblemWithMedication:
            values.diabetes_isProblemWithMedication ?? false,
          highBloodPressure_isProblemWithMedication:
            values.highBloodPressure_isProblemWithMedication ?? false,
          depression_isProblemWithMedication:
            values.depression_isProblemWithMedication ?? false,
          depression_isSuicidalThoughts:
            values.depression_isSuicidalThoughts ?? false,
          highCholesterol_isProblemWithMedication:
            values.highCholesterol_isProblemWithMedication ?? false,

          // Step 2 boolean fields
          otherMedVisit: values.otherMedVisit ?? false,

          // Step 3 boolean fields
          isShortnessOfBreath: values.isShortnessOfBreath ?? false,
          smoking_isInterestedInQuitting:
            values.smoking_isInterestedInQuitting ?? false,
          alcohol_moreThanFourDrinks:
            values.alcohol_moreThanFourDrinks ?? false,
          alcohol_othersConcerned: values.alcohol_othersConcerned ?? false,
          fallen: values.fallen ?? false,
          balanceProblems: values.balanceProblems ?? false,
          isUnsafeRelationship: values.isUnsafeRelationship ?? false,
          isWearSeatbelt: values.isWearSeatbelt ?? false,
          isHivTestRequested: values.isHivTestRequested ?? false,
          isSleepApnea: values.isSleepApnea ?? false,
          isFeelingDown: values.isFeelingDown ?? false,
          isProblemWithMedication: values.isProblemWithMedication ?? false,
          isProblemBladderControl: values.isProblemBladderControl ?? false,
          discussEndOfLifeCare: values.discussEndOfLifeCare ?? false,
        };

        console.log("=== SUBMITTING PRE-APPOINTMENT FORM ===");
        console.log("Original form values:", values);
        console.log("Transformed values:", transformedValues);
        console.log("Appointment ID:", transformedValues.appointmentId);
        console.log("=======================================");

        // Call the API to create pre-appointment form
        const response =
          await createPreAppointmentForm_apiCalls(transformedValues);

        console.log("=== PRE-APPOINTMENT FORM RESPONSE ===");
        console.log("API Response:", response);
        console.log("=====================================");

        toast({
          title: "Success",
          description: "Pre-appointment form submitted successfully!",
          variant: "default",
        });

        // Show success dialog
        setIsSuccessDialogOpen(true);

        // Navigate to appointments page after 2 seconds
        setTimeout(() => {
          navigate("/patient/appointments");
        }, 2000);

        // Reset form
        form.reset();
        setActiveStep(1);
      } catch (error) {
        console.error("=== PRE-APPOINTMENT FORM ERROR ===");
        console.error("Error:", error);
        console.error("==================================");

        toast({
          title: "Error",
          description:
            error?.message ||
            "Failed to submit pre-appointment form. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsSubmitting(false);
      }
    },
    [form, toast, navigate],
  );

  // Render the appropriate step component based on activeStep
  const renderStepComponent = () => {
    switch (activeStep) {
      case 1:
        return (
          <Step1Form
            form={form}
            firstInputRef={firstInputRef}
            isSubmitting={isSubmitting}
          />
        );
      case 2:
        return (
          <Step2Form
            form={form}
            firstInputRef={firstInputRef}
            isSubmitting={isSubmitting}
          />
        );
      case 3:
        return (
          <Step3Form
            form={form}
            firstInputRef={firstInputRef}
            isSubmitting={isSubmitting}
          />
        );
      case 4:
        return (
          <Step4Form
            form={form}
            firstInputRef={firstInputRef}
            isSubmitting={isSubmitting}
          />
        );
      default:
        return null;
    }
  };

  return (
    <div className="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
      <div className="container mx-auto px-4 py-6">
        {activeStep > 1 && (
          <button
            className="mb-4 p-2 rounded-full border hover:bg-gray-100 transition-colors"
            onClick={handlePrevious}
            aria-label="Go back"
            disabled={isSubmitting}
          >
            <ChevronLeft className="w-4 h-4" />
          </button>
        )}

        <div className="bg-white rounded-lg shadow-sm border p-6 max-w-4xl mx-auto">
          <h1 className="text-center text-lg font-medium mb-8">
            Please Provide following detail for better understanding
          </h1>

          {/* Progress Steps - Improved for mobile */}
          <div className="flex flex-wrap justify-center items-center mb-8 gap-2">
            <div className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${activeStep >= 1 ? "bg-primary text-white" : "bg-white text-primary border-primary border-[1px]"}`}
              >
                1
              </div>
              <span className="ml-2 mr-4 font-medium text-sm sm:text-base">
                Today&apos;s Visit
              </span>
              <div className="w-8 sm:w-16 border-t border-dashed border-gray-300 hidden sm:block"></div>
            </div>

            <div className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${activeStep >= 2 ? "bg-primary text-white" : "bg-white text-primary border border-primary"}`}
              >
                2
              </div>
              <span className="ml-2 mr-4 font-medium text-sm sm:text-base">
                Between Visits
              </span>
              <div className="w-8 sm:w-16 border-t border-dashed border-gray-300 hidden sm:block"></div>
            </div>

            <div className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${activeStep >= 3 ? "bg-primary text-white" : "bg-white text-primary border border-primary"}`}
              >
                3
              </div>
              <span className="ml-2 mr-4 font-medium text-sm sm:text-base">
                Lifestyle
              </span>
              <div className="w-8 sm:w-16 border-t border-dashed border-gray-300 hidden sm:block"></div>
            </div>

            <div className="flex items-center">
              <div
                className={`w-8 h-8 rounded-full flex items-center justify-center font-bold ${activeStep >= 4 ? "bg-primary text-white" : "bg-white text-primary border border-primary"}`}
              >
                4
              </div>
              <span className="ml-2 font-medium text-sm sm:text-base">
                Update
              </span>
            </div>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              {/* Render the appropriate step component */}
              <div className="border-solid border-[1px] border-[#E7E8E9] p-6 rounded-lg">
                {renderStepComponent()}
              </div>

              <div className="flex justify-end mt-6">
                {activeStep === 4 ? (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      type="button"
                      onClick={() => {
                        form.reset();
                        setActiveStep(1);
                      }}
                      disabled={isSubmitting}
                      className="bg-gray-100 text-gray-700 hover:bg-gray-200"
                    >
                      Cancel
                    </Button>
                    <Button
                      variant="default"
                      className="bg-blue-600 hover:bg-blue-700 text-white"
                      type="submit"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? (
                        <>
                          <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                          Processing...
                        </>
                      ) : (
                        "Save"
                      )}
                    </Button>
                  </div>
                ) : (
                  <Button
                    onClick={handleNext}
                    className="bg-blue-600 hover:bg-blue-700 text-white"
                    type="button"
                    disabled={isSubmitting}
                  >
                    Next
                  </Button>
                )}
              </div>
            </form>
          </Form>
        </div>
      </div>

      {/* Success Dialog */}
      <SuccessDialog
        isOpen={isSuccessDialogOpen}
        onClose={() => setIsSuccessDialogOpen(false)}
      />
    </div>
  );
};

// Success Dialog Component
const SuccessDialog = ({ isOpen, onClose }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="h-[400px] w-[400px] overflow-auto p-0 flex items-center justify-center flex-col px-6">
        <DialogTitle className="sr-only">Patient Information</DialogTitle>
        <DialogDescription className="sr-only">
          Enter or update your personal and medical information
        </DialogDescription>
        <img
          alt="Success"
          src={CheckCircle || "/placeholder.svg"}
          className="w-24 h-24"
        />
        <h2 className="font-bold text-[#16A34A] text-[20px]">Success</h2>
        <h2 className="font-[600] text-[#1E1E1E] text-[16px] text-center">
          Your pre-appointment form has been submitted successfully
        </h2>
      </DialogContent>
    </Dialog>
  );
};

export default PatientQuestionnaire;
