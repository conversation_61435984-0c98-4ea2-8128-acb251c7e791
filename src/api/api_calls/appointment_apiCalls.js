import { API_URLS } from "../api_config/api_routes";
import { request } from "../api_config/api_config";

// APPOINTMENT BOOKING API
export const bookAppointment_apiCalls = async ({
  doctorId,
  consultationId,
  startDateTime,
  endDateTime,
}) => {
  try {
    const res = await request.post(API_URLS.book_patient_appointment_api_url, {
      doctorId,
      consultationId,
      startDateTime,
      endDateTime,
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

// PRE-APPOINTMENT FORM APIs
export const createPreAppointmentForm_apiCalls = async (formData) => {
  try {
    const res = await request.post(API_URLS.create_preAppointmentForm_api_url, formData);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const updatePreAppointmentForm_apiCalls = async (formData) => {
  try {
    const res = await request.put(API_URLS.update_preAppointmentForm_api_url, formData);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getPreAppointmentFormById_apiCalls = async ({ id }) => {
  try {
    const res = await request.get(`${API_URLS.get_preAppointmentForm_by_id_api_url}/${id}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getPreAppointmentFormByAppointmentId_apiCalls = async ({ appointmentId }) => {
  try {
    const res = await request.get(`${API_URLS.get_preAppointmentForm_by_appointmentId_api_url}/${appointmentId}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getPreAppointmentFormByPatientId_apiCalls = async ({ patientId }) => {
  try {
    const res = await request.get(`${API_URLS.get_preAppointmentForm_by_patientId_api_url}/${patientId}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

// APPOINTMENT RETRIEVAL APIs
export const getAppointmentById_apiCalls = async ({ id }) => {
  try {
    const res = await request.get(`${API_URLS.get_appointment_by_id_api_url}/${id}`);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getAppointmentsByPatientId_apiCalls = async ({
  patientId,
  offset = 0,
  limit = 10,
  appointmentStatus,
  searchText,
  startDate,
  endDate
}) => {
  try {
    const params = new URLSearchParams();
    if (offset) params.append('offset', offset.toString());
    if (limit) params.append('limit', limit.toString());
    if (appointmentStatus) params.append('appointmentStatus', appointmentStatus);
    if (searchText) params.append('searchText', searchText);
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    const url = `${API_URLS.get_appointments_by_patientId_api_url}/${patientId}${params.toString() ? `?${params.toString()}` : ''}`;
    const res = await request.get(url);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const getAppointmentsByDoctorId_apiCalls = async ({
  offset = 0,
  limit = 10,
  appointmentStatus,
  searchText,
  startDate,
  endDate
}) => {
  try {
    const params = new URLSearchParams();
    if (offset) params.append('offset', offset.toString());
    if (limit) params.append('limit', limit.toString());
    if (appointmentStatus) params.append('appointmentStatus', appointmentStatus);
    if (searchText) params.append('searchText', searchText);
    if (startDate) params.append('startDate', startDate);
    if (endDate) params.append('endDate', endDate);

    // No doctorId needed as per backend - it uses the authenticated user
    const url = `${API_URLS.get_appointments_by_doctorId_api_url}${params.toString() ? `?${params.toString()}` : ''}`;
    const res = await request.get(url);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};
