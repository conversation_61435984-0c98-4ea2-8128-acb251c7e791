import { API_URLS } from "../api_config/api_routes";
import { request } from "../api_config/api_config";

export const getConcernedDoctors_api = async ({ patientId }) => {
  try {
    const url = `${API_URLS.get_concerned_doctors_api_url}/${patientId}`;
    const res = await request.get(url);
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};

export const shareEMR_api = async ({ doctorId, patientId }) => {
  try {
    const res = await request.post(API_URLS.share_emr_api_url, {
      doctorId,
      patientId,
    });
    if (res) {
      return res.data;
    }
  } catch (error) {
    // console.log("ERROR IN API CALL => ", error);
    throw new Error(error.message);
  }
};
