import { API_URLS } from "../../api_config/api_routes";
import { request } from "../../api_config/api_config";

// ================================
// PRESCRIPTION FORM APIs
// ================================

export const createPrescriptionForm_apiCalls = async (formData) => {
  try {
    console.log("🚀 Creating prescription form with data:", formData);
    console.log("📍 API URL:", API_URLS.create_prescriptionForm_api_url);

    const res = await request.post(API_URLS.create_prescriptionForm_api_url, formData);
    console.log("✅ Create prescription form response:", res);

    if (res) {
      return res.data;
    }
  } catch (error) {
    console.error("❌ Create prescription form error:", error);
    console.error("❌ Error response:", error.response?.data);
    console.error("❌ Error status:", error.response?.status);
    throw new Error(error.message);
  }
};

export const updatePrescriptionForm_apiCalls = async (formData) => {
  try {
    console.log("🔄 Updating prescription form with data:", formData);
    console.log("📍 API URL:", API_URLS.update_prescriptionForm_api_url);

    const res = await request.put(API_URLS.update_prescriptionForm_api_url, formData);
    console.log("✅ Update prescription form response:", res);

    if (res) {
      return res.data;
    }
  } catch (error) {
    console.error("❌ Update prescription form error:", error);
    console.error("❌ Error response:", error.response?.data);
    console.error("❌ Error status:", error.response?.status);
    throw new Error(error.message);
  }
};

export const getPrescriptionFormById_apiCalls = async ({ id }) => {
  try {
    console.log("📖 Getting prescription form by ID:", id);
    
    const res = await request.get(`${API_URLS.get_prescriptionForm_by_id_api_url}/${id}`);
    console.log("✅ Get prescription form by ID response:", res);
    
    if (res) {
      return res.data;
    }
  } catch (error) {
    console.error("❌ Get prescription form by ID error:", error);
    throw new Error(error.message);
  }
};

export const getPrescriptionFormByAppointmentId_apiCalls = async ({ appointmentId }) => {
  try {
    console.log("📖 Getting prescription form by appointment ID:", appointmentId);
    
    const res = await request.get(`${API_URLS.get_prescriptionForm_by_appointmentId_api_url}/${appointmentId}`);
    console.log("✅ Get prescription form by appointment ID response:", res);
    
    if (res) {
      return res.data;
    }
  } catch (error) {
    console.error("❌ Get prescription form by appointment ID error:", error);
    throw new Error(error.message);
  }
};

export const deletePrescriptionForm_apiCalls = async ({ id }) => {
  try {
    console.log("🗑️ Deleting prescription form with ID:", id);
    console.log("📍 API URL:", `${API_URLS.delete_prescriptionForm_api_url}/${id}`);

    const res = await request.delete(`${API_URLS.delete_prescriptionForm_api_url}/${id}`);
    console.log("✅ Delete prescription form response:", res);

    if (res) {
      return res.data;
    }
  } catch (error) {
    console.error("❌ Delete prescription form error:", error);
    console.error("❌ Error response:", error.response?.data);
    console.error("❌ Error status:", error.response?.status);
    throw new Error(error.message);
  }
};
